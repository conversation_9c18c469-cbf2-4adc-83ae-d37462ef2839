# BTC Trading Bot PROFESIONAL v3.0 - VERSIÓN MEJORADA Y OPTIMIZADA
# Sistema avanzado con modelos de ML mejorados y gestión precisa de TP/SL
# Diseñado para ejecutar UN TRADE POR VEZ con máxima precisión

import ccxt
import pandas as pd
import numpy as np
import talib
import time
import datetime
from sklearn.preprocessing import StandardScaler, RobustScaler
from sklearn.ensemble import RandomForestClassifier, VotingClassifier, GradientBoostingClassifier
from sklearn.linear_model import LogisticRegression
from sklearn.svm import SVC
from sklearn.model_selection import TimeSeriesSplit, GridSearchCV
from sklearn.metrics import classification_report, accuracy_score
from xgboost import XGBClassifier
import lightgbm as lgb
import plotly.graph_objects as go
from plotly.subplots import make_subplots
import json
import os
import pickle
import warnings
warnings.filterwarnings('ignore')

# Configuración MEJORADA v3.0
LOOKBACK = 50  # Más datos históricos para mejor predicción
SYMBOL = 'BTC/USDT'
INITIAL_CAPITAL = 10000
MAX_RISK_PER_TRADE = 0.03  # 3% por trade (más agresivo)
MAX_DAILY_LOSS = 0.08  # 8% pérdida máxima diaria
MIN_WIN_RATE = 0.45  # 45% mínimo
MIN_PROFIT_FACTOR = 1.2
MIN_CONFIDENCE = 0.65  # 65% mínimo de confianza (más estricto)
MAX_POSITIONS = 1  # SOLO UN TRADE POR VEZ
TRAILING_STOP_ENABLED = True  # Activar trailing stop
TP_SL_TOLERANCE = 0.0001  # Tolerancia para detección precisa de TP/SL (0.01%)

# Crear directorios
for dir_name in ['modelos', 'logs', 'trades', 'reports']:
    os.makedirs(dir_name, exist_ok=True)

class TradingLogger:
    """Sistema de logging mejorado"""
    def __init__(self):
        self.log_file = f"logs/trading_{datetime.datetime.now().strftime('%Y%m%d')}.log"

    def log(self, message, level="INFO"):
        timestamp = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        formatted_message = f"{timestamp} [{level}] {message}"
        print(formatted_message)

        try:
            with open(self.log_file, "a", encoding="utf-8") as f:
                f.write(formatted_message + "\n")
        except:
            pass

logger = TradingLogger()

class DataManager:
    """Gestión profesional de datos"""
    def __init__(self):
        self.exchange = None

    def get_live_data(self, symbol=SYMBOL, timeframe='15m', limit=500):
        """Obtiene datos en vivo de Binance"""
        try:
            if not self.exchange:
                self.exchange = ccxt.binance({
                    'enableRateLimit': True,
                    'options': {'defaultType': 'spot'}
                })

            ohlcv = self.exchange.fetch_ohlcv(symbol, timeframe=timeframe, limit=limit)
            df = pd.DataFrame(ohlcv, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['timestamp'] = pd.to_datetime(df['timestamp'], unit='ms')
            df.set_index('timestamp', inplace=True)

            logger.log(f"✅ Datos obtenidos: {len(df)} velas de {timeframe}")
            return df

        except Exception as e:
            logger.log(f"❌ Error obteniendo datos: {str(e)}", "ERROR")
            return self.get_mock_data(timeframe, limit)

    def get_mock_data(self, timeframe='15m', limit=500):
        """Datos simulados realistas para testing"""
        np.random.seed(42)
        freq_map = {'5m': '5T', '15m': '15T', '1h': '1H', '4h': '4H'}

        timestamps = pd.date_range(end=pd.Timestamp.now(), periods=limit, freq=freq_map[timeframe])

        # Simulación más realista con tendencias y volatilidad variable
        base_price = 98000
        drift = 0.0001
        volatility = 0.001

        prices = [base_price]
        for i in range(1, limit):
            change = drift + np.random.normal(0, volatility)
            new_price = prices[-1] * (1 + change)
            prices.append(new_price)

        close = np.array(prices)
        open_prices = np.roll(close, 1)
        open_prices[0] = base_price

        high = np.maximum(close, open_prices) * (1 + np.random.uniform(0, 0.002, limit))
        low = np.minimum(close, open_prices) * (1 - np.random.uniform(0, 0.002, limit))
        volume = np.random.lognormal(10, 0.5, limit) * 1000

        df = pd.DataFrame({
            'open': open_prices,
            'high': high,
            'low': low,
            'close': close,
            'volume': volume
        }, index=timestamps)

        logger.log(f"✅ Datos simulados generados: {len(df)} velas")
        return df

class FeatureEngineer:
    """Feature Engineering avanzado y optimizado v3.0"""

    @staticmethod
    def add_price_features(df):
        """Features avanzados de precio"""
        # Returns múltiples períodos
        for period in [1, 3, 5, 10, 20]:
            df[f'returns_{period}'] = df['close'].pct_change(period)
            df[f'log_returns_{period}'] = np.log(df['close'] / df['close'].shift(period))

        # Volatility múltiples ventanas
        for window in [10, 20, 50]:
            df[f'volatility_{window}'] = df['returns'].rolling(window).std()
            df[f'volatility_ratio_{window}'] = df[f'volatility_{window}'] / df[f'volatility_{window}'].rolling(window*2).mean()

        # Price ratios avanzados
        df['hl_ratio'] = (df['high'] - df['low']) / df['close']
        df['co_ratio'] = (df['close'] - df['open']) / df['open']
        df['oc_ratio'] = (df['open'] - df['close'].shift(1)) / df['close'].shift(1)  # Gap
        df['body_ratio'] = abs(df['close'] - df['open']) / (df['high'] - df['low'] + 1e-8)
        df['upper_shadow'] = (df['high'] - np.maximum(df['open'], df['close'])) / df['close']
        df['lower_shadow'] = (np.minimum(df['open'], df['close']) - df['low']) / df['close']

        # Momentum features
        df['price_momentum_5'] = df['close'] / df['close'].shift(5) - 1
        df['price_momentum_10'] = df['close'] / df['close'].shift(10) - 1
        df['price_acceleration'] = df['returns'] - df['returns'].shift(1)

        return df

    @staticmethod
    def add_technical_indicators(df):
        """Indicadores técnicos avanzados y optimizados"""
        # RSI múltiples períodos
        for period in [7, 14, 21]:
            df[f'rsi_{period}'] = talib.RSI(df['close'], timeperiod=period)
            df[f'rsi_overbought_{period}'] = (df[f'rsi_{period}'] > 70).astype(int)
            df[f'rsi_oversold_{period}'] = (df[f'rsi_{period}'] < 30).astype(int)

        # RSI divergence
        df['rsi_divergence'] = df['rsi_14'] - df['rsi_14'].shift(5)

        # Moving Averages múltiples
        for period in [10, 20, 50, 100, 200]:
            df[f'sma_{period}'] = talib.SMA(df['close'], timeperiod=period)
            df[f'ema_{period}'] = talib.EMA(df['close'], timeperiod=period)
            df[f'price_vs_sma_{period}'] = (df['close'] - df[f'sma_{period}']) / df[f'sma_{period}']
            df[f'price_vs_ema_{period}'] = (df['close'] - df[f'ema_{period}']) / df[f'ema_{period}']

        # MA crosses
        df['sma_cross_20_50'] = np.where(df['sma_20'] > df['sma_50'], 1, -1)
        df['ema_cross_12_26'] = np.where(df['ema_12'] > df['ema_26'], 1, -1)

        # MACD múltiples configuraciones
        df['macd'], df['macd_signal'], df['macd_hist'] = talib.MACD(df['close'])
        df['macd_cross'] = np.where(df['macd'] > df['macd_signal'], 1, -1)
        df['macd_momentum'] = df['macd_hist'] - df['macd_hist'].shift(1)

        # Bollinger Bands
        for period in [20, 50]:
            bb_upper, bb_middle, bb_lower = talib.BBANDS(df['close'], timeperiod=period)
            df[f'bb_upper_{period}'] = bb_upper
            df[f'bb_middle_{period}'] = bb_middle
            df[f'bb_lower_{period}'] = bb_lower
            df[f'bb_width_{period}'] = (bb_upper - bb_lower) / bb_middle
            df[f'bb_position_{period}'] = (df['close'] - bb_lower) / (bb_upper - bb_lower)
            df[f'bb_squeeze_{period}'] = (df[f'bb_width_{period}'] < df[f'bb_width_{period}'].rolling(20).mean()).astype(int)

        # ATR múltiples períodos - CRÍTICO PARA STOP LOSS Y TAKE PROFIT
        for period in [14, 21, 50]:
            df[f'atr_{period}'] = talib.ATR(df['high'], df['low'], df['close'], timeperiod=period)
            df[f'atr_ratio_{period}'] = df[f'atr_{period}'] / df['close']

        # Stochastic
        df['stoch_k'], df['stoch_d'] = talib.STOCH(df['high'], df['low'], df['close'])
        df['stoch_cross'] = np.where(df['stoch_k'] > df['stoch_d'], 1, -1)

        # Williams %R
        df['williams_r'] = talib.WILLR(df['high'], df['low'], df['close'])

        # CCI
        df['cci'] = talib.CCI(df['high'], df['low'], df['close'])

        # Volume indicators avanzados
        df['volume_sma_20'] = df['volume'].rolling(20).mean()
        df['volume_ratio'] = df['volume'] / df['volume_sma_20']
        df['volume_momentum'] = df['volume'] / df['volume'].shift(1) - 1

        # OBV (On Balance Volume)
        df['obv'] = talib.OBV(df['close'], df['volume'])
        df['obv_sma'] = df['obv'].rolling(20).mean()
        df['obv_ratio'] = df['obv'] / df['obv_sma']

        return df

    @staticmethod
    def add_pattern_features(df):
        """Patrones de velas japonesas avanzados"""
        # Patrones básicos
        df['is_green'] = (df['close'] > df['open']).astype(int)
        df['body_size'] = abs(df['close'] - df['open']) / df['close']
        df['shadow_ratio'] = ((df['high'] - df['low']) - abs(df['close'] - df['open'])) / df['close']

        # Patrones consecutivos
        for window in [3, 5, 7]:
            df[f'green_streak_{window}'] = df['is_green'].rolling(window).sum()
            df[f'red_streak_{window}'] = (1 - df['is_green']).rolling(window).sum()

        # Patrones de reversión
        df['hammer'] = ((df['lower_shadow'] > 2 * abs(df['close'] - df['open'])) &
                       (df['upper_shadow'] < 0.1 * abs(df['close'] - df['open']))).astype(int)
        df['shooting_star'] = ((df['upper_shadow'] > 2 * abs(df['close'] - df['open'])) &
                              (df['lower_shadow'] < 0.1 * abs(df['close'] - df['open']))).astype(int)
        df['doji'] = (abs(df['close'] - df['open']) < 0.001 * df['close']).astype(int)

        # Gaps
        df['gap_up'] = ((df['open'] - df['close'].shift(1)) / df['close'].shift(1) > 0.002).astype(int)
        df['gap_down'] = ((df['close'].shift(1) - df['open']) / df['close'].shift(1) > 0.002).astype(int)

        return df

    @staticmethod
    def add_market_structure_features(df):
        """Features de estructura de mercado"""
        # Support and Resistance levels
        df['local_high'] = df['high'].rolling(5, center=True).max() == df['high']
        df['local_low'] = df['low'].rolling(5, center=True).min() == df['low']

        # Trend strength
        df['trend_strength_20'] = (df['close'] - df['close'].shift(20)) / df['atr_14']
        df['trend_strength_50'] = (df['close'] - df['close'].shift(50)) / df['atr_14']

        # Market regime
        df['volatility_regime'] = (df['atr_14'] > df['atr_14'].rolling(50).mean()).astype(int)
        df['volume_regime'] = (df['volume'] > df['volume'].rolling(50).mean()).astype(int)

        # Price position in range
        high_20 = df['high'].rolling(20).max()
        low_20 = df['low'].rolling(20).min()
        df['price_position_20'] = (df['close'] - low_20) / (high_20 - low_20 + 1e-8)

        return df

    @staticmethod
    def create_features(df):
        """Pipeline completo de features mejorado v3.0"""
        df = FeatureEngineer.add_price_features(df)
        df = FeatureEngineer.add_technical_indicators(df)
        df = FeatureEngineer.add_pattern_features(df)
        df = FeatureEngineer.add_market_structure_features(df)

        # Limpiar NaN e infinitos
        df = df.replace([np.inf, -np.inf], np.nan)
        df = df.fillna(method='ffill').fillna(0)

        logger.log(f"✅ Features avanzados creados: {len(df.columns)} columnas")
        return df

class SignalGenerator:
    """Generador de señales avanzado v3.0"""

    def __init__(self):
        self.models = {}
        self.scaler = RobustScaler()  # Más robusto a outliers
        self.feature_cols = None
        self.feature_importance = {}
        self.model_weights = {}
        self.validation_scores = {}

    def prepare_data(self, df, lookback=LOOKBACK):
        """Prepara datos para ML con targets mejorados"""
        # Crear múltiples targets para diferentes horizontes
        for horizon in [1, 3, 5]:
            df[f'future_return_{horizon}'] = df['close'].pct_change(horizon).shift(-horizon)

        # Target principal más sofisticado
        # Usar ATR para normalizar los umbrales
        atr_threshold = df['atr_14'] / df['close']

        # Target basado en ATR dinámico
        df['target'] = np.where(
            df['future_return_3'] > atr_threshold * 0.5, 1,  # LONG
            np.where(df['future_return_3'] < -atr_threshold * 0.5, -1, 0)  # SHORT
        )

        # Target adicional para volatilidad
        df['volatility_target'] = np.where(
            df['atr_14'] > df['atr_14'].rolling(20).mean() * 1.2, 1, 0
        )

        # Para ML necesitamos 0, 1, 2
        df['target_ml'] = df['target'] + 1

        # Seleccionar features (excluir más columnas)
        exclude_cols = [
            'open', 'high', 'low', 'close', 'volume',
            'future_return_1', 'future_return_3', 'future_return_5',
            'target', 'target_ml', 'volatility_target',
            'local_high', 'local_low'  # Excluir features que usan center=True
        ]
        self.feature_cols = [col for col in df.columns if col not in exclude_cols]

        # Eliminar filas con NaN
        df_clean = df.dropna()

        if len(df_clean) < lookback + 50:  # Más datos requeridos
            logger.log("⚠️ No hay suficientes datos para entrenamiento avanzado", "WARNING")
            return None, None

        X = df_clean[self.feature_cols].values
        y = df_clean['target_ml'].values

        logger.log(f"📊 Datos preparados: {len(X)} muestras, {len(self.feature_cols)} features")
        return X, y

    def train_models(self, X_train, y_train):
        """Entrena ensemble de modelos avanzados con validación"""
        logger.log("🤖 Entrenando modelos avanzados...")

        # Validación temporal
        tscv = TimeSeriesSplit(n_splits=3)

        # Modelos base mejorados
        models = {
            'rf_optimized': RandomForestClassifier(
                n_estimators=200,
                max_depth=8,
                min_samples_split=10,
                min_samples_leaf=5,
                max_features='sqrt',
                random_state=42,
                n_jobs=-1,
                class_weight='balanced'
            ),
            'xgb_optimized': XGBClassifier(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.05,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=0.1,
                random_state=42,
                n_jobs=-1,
                eval_metric='mlogloss'
            ),
            'lgb_optimized': lgb.LGBMClassifier(
                n_estimators=200,
                max_depth=6,
                learning_rate=0.05,
                subsample=0.8,
                colsample_bytree=0.8,
                reg_alpha=0.1,
                reg_lambda=0.1,
                random_state=42,
                n_jobs=-1,
                class_weight='balanced',
                verbose=-1
            ),
            'gb_optimized': GradientBoostingClassifier(
                n_estimators=100,
                max_depth=5,
                learning_rate=0.1,
                subsample=0.8,
                random_state=42
            ),
            'lr_optimized': LogisticRegression(
                C=1.0,
                max_iter=2000,
                random_state=42,
                n_jobs=-1,
                class_weight='balanced'
            )
        }

        # Entrenar y validar modelos
        for name, model in models.items():
            try:
                # Validación cruzada temporal
                cv_scores = []
                for train_idx, val_idx in tscv.split(X_train):
                    X_fold_train, X_fold_val = X_train[train_idx], X_train[val_idx]
                    y_fold_train, y_fold_val = y_train[train_idx], y_train[val_idx]

                    model.fit(X_fold_train, y_fold_train)
                    score = accuracy_score(y_fold_val, model.predict(X_fold_val))
                    cv_scores.append(score)

                # Entrenar en todos los datos
                model.fit(X_train, y_train)

                # Guardar modelo y métricas
                self.models[name] = model
                self.validation_scores[name] = np.mean(cv_scores)

                # Feature importance para modelos que lo soportan
                if hasattr(model, 'feature_importances_'):
                    self.feature_importance[name] = model.feature_importances_

                logger.log(f"✅ Modelo {name} entrenado - CV Score: {np.mean(cv_scores):.3f}")

            except Exception as e:
                logger.log(f"❌ Error entrenando {name}: {str(e)}", "ERROR")

        # Calcular pesos basados en rendimiento
        if self.validation_scores:
            total_score = sum(self.validation_scores.values())
            for name, score in self.validation_scores.items():
                self.model_weights[name] = score / total_score

        # Ensemble voting con pesos
        if len(self.models) > 0:
            self.ensemble = VotingClassifier(
                estimators=list(self.models.items()),
                voting='soft'
            )
            self.ensemble.fit(X_train, y_train)
            logger.log("✅ Ensemble avanzado creado")

            # Mostrar resumen de modelos
            logger.log("\n📊 RESUMEN DE MODELOS:")
            for name, score in self.validation_scores.items():
                weight = self.model_weights.get(name, 0)
                logger.log(f"├─ {name}: CV={score:.3f}, Peso={weight:.3f}")

        # Guardar modelos entrenados
        self.save_models()

    def save_models(self):
        """Guarda modelos entrenados"""
        try:
            model_data = {
                'models': self.models,
                'scaler': self.scaler,
                'feature_cols': self.feature_cols,
                'validation_scores': self.validation_scores,
                'model_weights': self.model_weights,
                'feature_importance': self.feature_importance
            }

            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"modelos/trading_models_{timestamp}.pkl"

            with open(filename, 'wb') as f:
                pickle.dump(model_data, f)

            logger.log(f"💾 Modelos guardados en: {filename}")

        except Exception as e:
            logger.log(f"❌ Error guardando modelos: {str(e)}", "ERROR")

    def load_models(self, filename=None):
        """Carga modelos entrenados"""
        try:
            if filename is None:
                # Buscar el archivo más reciente
                import glob
                model_files = glob.glob("modelos/trading_models_*.pkl")
                if not model_files:
                    return False
                filename = max(model_files, key=os.path.getctime)

            with open(filename, 'rb') as f:
                model_data = pickle.load(f)

            self.models = model_data['models']
            self.scaler = model_data['scaler']
            self.feature_cols = model_data['feature_cols']
            self.validation_scores = model_data.get('validation_scores', {})
            self.model_weights = model_data.get('model_weights', {})
            self.feature_importance = model_data.get('feature_importance', {})

            # Recrear ensemble
            if len(self.models) > 0:
                self.ensemble = VotingClassifier(
                    estimators=list(self.models.items()),
                    voting='soft'
                )
                # El ensemble necesita ser entrenado, pero podemos usar los modelos individuales

            logger.log(f"📂 Modelos cargados desde: {filename}")
            return True

        except Exception as e:
            logger.log(f"❌ Error cargando modelos: {str(e)}", "ERROR")
            return False

    def generate_signals(self, df):
        """Genera señales avanzadas con múltiples validaciones"""
        if not self.models:
            logger.log("⚠️ No hay modelos entrenados", "WARNING")
            return pd.DataFrame()

        X, _ = self.prepare_data(df)
        if X is None:
            return pd.DataFrame()

        # Escalar datos
        X_scaled = self.scaler.transform(X)

        # Predicciones individuales de cada modelo
        individual_predictions = {}
        individual_probabilities = {}

        try:
            for name, model in self.models.items():
                pred = model.predict(X_scaled)
                prob = model.predict_proba(X_scaled)
                individual_predictions[name] = pred
                individual_probabilities[name] = prob

            # Ensemble ponderado personalizado
            weighted_probabilities = np.zeros((len(X_scaled), 3))

            for name, prob in individual_probabilities.items():
                weight = self.model_weights.get(name, 1.0 / len(self.models))
                weighted_probabilities += prob * weight

            # Normalizar probabilidades
            weighted_probabilities = weighted_probabilities / np.sum(weighted_probabilities, axis=1, keepdims=True)

            # Predicciones finales
            predictions = np.argmax(weighted_probabilities, axis=1)
            confidence = np.max(weighted_probabilities, axis=1)

            # Filtros adicionales de calidad
            # 1. Filtro de confianza mínima
            high_confidence_mask = confidence >= MIN_CONFIDENCE

            # 2. Filtro de consenso entre modelos
            consensus_mask = np.zeros(len(predictions), dtype=bool)
            for i in range(len(predictions)):
                model_votes = [individual_predictions[name][i] for name in self.models.keys()]
                most_common = max(set(model_votes), key=model_votes.count)
                consensus_count = model_votes.count(most_common)
                consensus_mask[i] = consensus_count >= len(self.models) * 0.6  # 60% consenso

            # 3. Filtro de volatilidad (evitar trades en alta volatilidad extrema)
            if 'atr_14' in df.columns:
                current_atr = df['atr_14'].iloc[-len(predictions):]
                atr_threshold = current_atr.rolling(50).mean() * 2.0
                volatility_mask = current_atr <= atr_threshold
            else:
                volatility_mask = np.ones(len(predictions), dtype=bool)

            # Combinar todos los filtros
            final_mask = high_confidence_mask & consensus_mask & volatility_mask
            filtered_predictions = np.where(final_mask, predictions, 1)  # 1 = NEUTRAL

            # Mapear predicciones: 0=SHORT, 1=NEUTRAL, 2=LONG
            signal_map = {0: -1, 1: 0, 2: 1}
            mapped_signals = np.array([signal_map.get(int(p), 0) for p in filtered_predictions])

            # Añadir ATR para cálculo de TP/SL
            atr_values = df['atr_14'].iloc[-len(predictions):].values if 'atr_14' in df.columns else np.full(len(predictions), df['close'].mean() * 0.005)

            # Crear DataFrame de señales
            signals = pd.DataFrame({
                'timestamp': df.index[-len(predictions):],
                'signal': mapped_signals,
                'confidence': confidence,
                'price': df['close'].iloc[-len(predictions):].values,
                'atr': atr_values,
                'consensus_score': [consensus_mask[i] for i in range(len(predictions))],
                'volatility_ok': [volatility_mask[i] for i in range(len(predictions))]
            })

            # Solo señales no neutrales
            signals = signals[signals['signal'] != 0]

            # Log detallado de debug
            total_predictions = len(predictions)
            high_conf_count = np.sum(high_confidence_mask)
            consensus_count = np.sum(consensus_mask)
            volatility_ok_count = np.sum(volatility_mask)
            final_signals = len(signals)

            logger.log(f"📊 ANÁLISIS DE SEÑALES AVANZADO:")
            logger.log(f"├─ Total predicciones: {total_predictions}")
            logger.log(f"├─ Alta confianza (>{MIN_CONFIDENCE:.0%}): {high_conf_count}")
            logger.log(f"├─ Consenso entre modelos (>60%): {consensus_count}")
            logger.log(f"├─ Volatilidad aceptable: {volatility_ok_count}")
            logger.log(f"└─ Señales finales válidas: {final_signals}")

            if final_signals > 0:
                long_signals = len(signals[signals['signal'] == 1])
                short_signals = len(signals[signals['signal'] == -1])
                avg_confidence = signals['confidence'].mean()

                logger.log(f"\n🎯 SEÑALES DETECTADAS:")
                logger.log(f"├─ LONG: {long_signals}")
                logger.log(f"├─ SHORT: {short_signals}")
                logger.log(f"└─ Confianza promedio: {avg_confidence:.2%}")

            return signals

        except Exception as e:
            logger.log(f"❌ Error generando señales avanzadas: {str(e)}", "ERROR")
            return pd.DataFrame()

class RiskManager:
    """Gestión de riesgo avanzada v3.0 - UN TRADE POR VEZ"""

    def __init__(self, initial_capital=INITIAL_CAPITAL):
        self.initial_capital = initial_capital
        self.current_capital = initial_capital
        self.positions = []
        self.daily_pnl = 0
        self.trade_history = []
        self.max_drawdown = 0
        self.peak_capital = initial_capital
        self.consecutive_losses = 0
        self.last_trade_time = None

    def can_open_position(self):
        """Verifica si se puede abrir una nueva posición"""
        # Solo un trade por vez
        open_positions = [p for p in self.positions if p.get('status') == 'OPEN']
        if len(open_positions) >= MAX_POSITIONS:
            return False, "Máximo de posiciones alcanzado"

        # Verificar límite diario
        if self.check_daily_loss_limit():
            return False, "Límite de pérdida diaria alcanzado"

        # Verificar pérdidas consecutivas
        if self.consecutive_losses >= 3:
            return False, "Demasiadas pérdidas consecutivas"

        # Verificar tiempo mínimo entre trades
        if self.last_trade_time:
            time_since_last = (datetime.datetime.now() - self.last_trade_time).seconds
            if time_since_last < 300:  # 5 minutos mínimo
                return False, "Tiempo mínimo entre trades no cumplido"

        return True, "OK"

    def calculate_position_size(self, signal_confidence):
        """Calcula tamaño de posición avanzado"""
        # Kelly Criterion mejorado
        win_rate = self.get_recent_win_rate()
        avg_win = self.get_average_win()
        avg_loss = self.get_average_loss()

        if avg_loss > 0:
            kelly_fraction = (win_rate * avg_win - (1 - win_rate) * avg_loss) / avg_win
        else:
            kelly_fraction = (signal_confidence - 0.5) / 0.5

        # Ajustar por confianza y drawdown
        confidence_multiplier = signal_confidence
        drawdown_multiplier = max(0.5, 1 - self.max_drawdown / 0.2)  # Reducir size si hay drawdown

        kelly_fraction = kelly_fraction * confidence_multiplier * drawdown_multiplier
        kelly_fraction = max(0, min(kelly_fraction, 0.15))  # Máximo 15% Kelly

        # Tamaño base
        position_size = self.current_capital * kelly_fraction * MAX_RISK_PER_TRADE

        # Límites de seguridad
        max_position = self.current_capital * 0.08  # Máximo 8% del capital
        min_position = self.current_capital * 0.01  # Mínimo 1% del capital

        position_size = max(min_position, min(position_size, max_position))

        return position_size

    def get_recent_win_rate(self, lookback=10):
        """Calcula win rate de los últimos trades"""
        if len(self.trade_history) < 3:
            return 0.5  # Default

        recent_trades = self.trade_history[-lookback:]
        wins = sum(1 for trade in recent_trades if trade.get('pnl', 0) > 0)
        return wins / len(recent_trades)

    def get_average_win(self, lookback=10):
        """Calcula ganancia promedio"""
        if len(self.trade_history) < 3:
            return 0.02  # Default 2%

        recent_trades = self.trade_history[-lookback:]
        wins = [trade['pnl_pct'] for trade in recent_trades if trade.get('pnl', 0) > 0]
        return np.mean(wins) / 100 if wins else 0.02

    def get_average_loss(self, lookback=10):
        """Calcula pérdida promedio"""
        if len(self.trade_history) < 3:
            return 0.015  # Default 1.5%

        recent_trades = self.trade_history[-lookback:]
        losses = [abs(trade['pnl_pct']) for trade in recent_trades if trade.get('pnl', 0) < 0]
        return np.mean(losses) / 100 if losses else 0.015

    def calculate_stop_loss(self, entry_price, atr, direction):
        """Stop loss dinámico mejorado basado en ATR"""
        # ATR multiplier adaptativo basado en volatilidad
        base_multiplier = 2.0  # Más conservador

        # Ajustar por volatilidad reciente
        volatility_adjustment = min(1.5, max(0.8, atr / (entry_price * 0.01)))
        atr_multiplier = base_multiplier * volatility_adjustment

        if direction == 1:  # LONG
            stop_loss = entry_price - (atr * atr_multiplier)
        else:  # SHORT
            stop_loss = entry_price + (atr * atr_multiplier)

        return stop_loss

    def calculate_take_profit(self, entry_price, atr, direction, risk_reward=2.0):
        """Take profit mejorado con ratio riesgo/beneficio favorable"""
        # Ratio más agresivo para compensar comisiones
        tp_distance = atr * risk_reward

        if direction == 1:  # LONG
            take_profit = entry_price + tp_distance
        else:  # SHORT
            take_profit = entry_price - tp_distance

        return take_profit

    def update_trailing_stop(self, position, current_price):
        """Actualiza trailing stop loss"""
        if not TRAILING_STOP_ENABLED:
            return position

        if position['type'] == 'LONG':
            # Para LONG: mover SL hacia arriba si el precio sube
            if current_price > position['entry_price']:
                # Calcular nuevo trailing stop
                trail_distance = position.get('atr', current_price * 0.01) * 1.5
                new_stop = current_price - trail_distance

                # Solo actualizar si es mejor que el SL actual
                if new_stop > position['stop_loss']:
                    old_stop = position['stop_loss']
                    position['stop_loss'] = new_stop
                    position['trailing_updated'] = True
                    logger.log(f"📈 Trailing Stop actualizado LONG: ${old_stop:.2f} → ${new_stop:.2f}")

        else:  # SHORT
            # Para SHORT: mover SL hacia abajo si el precio baja
            if current_price < position['entry_price']:
                # Calcular nuevo trailing stop
                trail_distance = position.get('atr', current_price * 0.01) * 1.5
                new_stop = current_price + trail_distance

                # Solo actualizar si es mejor que el SL actual
                if new_stop < position['stop_loss']:
                    old_stop = position['stop_loss']
                    position['stop_loss'] = new_stop
                    position['trailing_updated'] = True
                    logger.log(f"📉 Trailing Stop actualizado SHORT: ${old_stop:.2f} → ${new_stop:.2f}")

        return position

    def check_daily_loss_limit(self):
        """Verifica límite de pérdida diaria mejorado"""
        daily_loss_pct = abs(self.daily_pnl / self.initial_capital)

        if daily_loss_pct >= MAX_DAILY_LOSS:
            logger.log(f"🛑 Límite diario alcanzado: {daily_loss_pct:.2%}", "WARNING")
            return True
        return False

    def update_capital(self, pnl, trade_result='win'):
        """Actualiza capital y estadísticas avanzadas"""
        self.current_capital += pnl
        self.daily_pnl += pnl

        # Actualizar peak capital y drawdown
        if self.current_capital > self.peak_capital:
            self.peak_capital = self.current_capital
            self.max_drawdown = 0
        else:
            current_drawdown = (self.peak_capital - self.current_capital) / self.peak_capital
            self.max_drawdown = max(self.max_drawdown, current_drawdown)

        # Actualizar pérdidas consecutivas
        if trade_result == 'loss':
            self.consecutive_losses += 1
        else:
            self.consecutive_losses = 0

        # Actualizar tiempo del último trade
        self.last_trade_time = datetime.datetime.now()

        # Log estado detallado
        logger.log(f"💰 Capital: ${self.current_capital:.2f} | PnL Diario: ${self.daily_pnl:.2f}")
        logger.log(f"📊 Drawdown: {self.max_drawdown:.2%} | Pérdidas consecutivas: {self.consecutive_losses}")

class SimulatedTrader:
    """Trading avanzado v3.0 - UN TRADE POR VEZ con detección precisa de TP/SL"""

    def __init__(self):
        self.data_manager = DataManager()
        self.feature_engineer = FeatureEngineer()
        self.signal_generator = SignalGenerator()
        self.risk_manager = RiskManager()
        self.active_positions = []
        self.trade_queue = []  # Cola de señales pendientes

    def train_system(self, df):
        """Entrena el sistema con datos históricos"""
        # Crear features
        df_features = self.feature_engineer.create_features(df.copy())

        # Preparar datos
        X, y = self.signal_generator.prepare_data(df_features)

        if X is not None and len(X) > 100:
            # Split 80/20
            split_idx = int(0.8 * len(X))
            X_train, y_train = X[:split_idx], y[:split_idx]

            # Escalar datos
            X_train_scaled = self.signal_generator.scaler.fit_transform(X_train)

            # Entrenar modelos
            self.signal_generator.train_models(X_train_scaled, y_train)

            logger.log("✅ Sistema entrenado y listo")
            logger.log(f"📊 Modelos activos: {list(self.signal_generator.models.keys())}")
            return True

        return False

    def execute_trade(self, signal, current_df):
        """Ejecuta trade mejorado con validaciones estrictas"""
        # Verificar si se puede abrir posición
        can_open, reason = self.risk_manager.can_open_position()
        if not can_open:
            logger.log(f"❌ No se puede abrir posición: {reason}")
            return None

        # Calcular parámetros
        position_size = self.risk_manager.calculate_position_size(signal['confidence'])

        # Obtener ATR actual del DataFrame (mejorado)
        atr_col = 'atr_14' if 'atr_14' in current_df.columns else 'atr'
        if atr_col in current_df.columns and not current_df[atr_col].empty:
            current_atr = current_df[atr_col].iloc[-1]
            if pd.isna(current_atr) or current_atr <= 0:
                current_atr = current_df['close'].iloc[-1] * 0.008  # 0.8% default
        else:
            current_atr = current_df['close'].iloc[-1] * 0.008

        stop_loss = self.risk_manager.calculate_stop_loss(
            signal['price'], current_atr, signal['signal']
        )

        take_profit = self.risk_manager.calculate_take_profit(
            signal['price'], current_atr, signal['signal']
        )

        trade = {
            'timestamp': signal['timestamp'],
            'type': 'LONG' if signal['signal'] == 1 else 'SHORT',
            'entry_price': signal['price'],
            'stop_loss': stop_loss,
            'take_profit': take_profit,
            'position_size': position_size,
            'confidence': signal['confidence'],
            'status': 'OPEN',
            'entry_time': datetime.datetime.now(),
            'atr': current_atr,
            'trailing_updated': False,
            'max_favorable_price': signal['price']  # Para tracking de trailing stop
        }

        self.active_positions.append(trade)

        # Log detallado mejorado
        risk_pct = abs(stop_loss - signal['price']) / signal['price'] * 100
        reward_pct = abs(take_profit - signal['price']) / signal['price'] * 100
        risk_reward_ratio = reward_pct / risk_pct if risk_pct > 0 else 0

        logger.log(f"\n{'='*70}")
        logger.log(f"🎯 NUEVA POSICIÓN ABIERTA (TRADE ÚNICO):")
        logger.log(f"├─ Tipo: {'🟢 LONG' if trade['type'] == 'LONG' else '🔴 SHORT'}")
        logger.log(f"├─ Entrada: ${trade['entry_price']:.2f}")
        logger.log(f"├─ Stop Loss: ${stop_loss:.2f} (-{risk_pct:.2f}%)")
        logger.log(f"├─ Take Profit: ${take_profit:.2f} (+{reward_pct:.2f}%)")
        logger.log(f"├─ Tamaño: ${position_size:.2f}")
        logger.log(f"├─ Confianza: {trade['confidence']:.2%}")
        logger.log(f"├─ Risk/Reward: 1:{risk_reward_ratio:.1f}")
        logger.log(f"├─ ATR: ${current_atr:.2f}")
        logger.log(f"└─ Trailing Stop: {'✅ Activado' if TRAILING_STOP_ENABLED else '❌ Desactivado'}")
        logger.log(f"{'='*70}\n")

        return trade

    def update_positions(self, current_price):
        """Actualiza posiciones con detección precisa de TP/SL y trailing stop"""
        for position in self.active_positions:
            if position['status'] != 'OPEN':
                continue

            # Actualizar precio máximo favorable para trailing stop
            if position['type'] == 'LONG':
                if current_price > position['max_favorable_price']:
                    position['max_favorable_price'] = current_price
            else:  # SHORT
                if current_price < position['max_favorable_price']:
                    position['max_favorable_price'] = current_price

            # Actualizar trailing stop
            position = self.risk_manager.update_trailing_stop(position, current_price)

            # Detección precisa con tolerancia
            tolerance = TP_SL_TOLERANCE

            if position['type'] == 'LONG':
                # Calcular distancias precisas
                dist_to_sl = (current_price - position['stop_loss']) / position['stop_loss']
                dist_to_tp = (position['take_profit'] - current_price) / current_price

                # Alertas de proximidad (más precisas)
                if 0 < dist_to_sl < 0.005:  # Dentro del 0.5%
                    logger.log(f"⚠️ ALERTA CRÍTICA: LONG muy cerca de SL! Distancia: {dist_to_sl*100:.3f}%")
                elif 0 < dist_to_tp < 0.005:  # Dentro del 0.5%
                    logger.log(f"🎯 ALERTA: LONG muy cerca de TP! Distancia: {dist_to_tp*100:.3f}%")

                # Detección precisa de stop loss
                if current_price <= (position['stop_loss'] * (1 + tolerance)):
                    logger.log(f"🛑 STOP LOSS ACTIVADO: Precio ${current_price:.2f} <= SL ${position['stop_loss']:.2f}")
                    self.close_position(position, current_price, 'STOP_LOSS')

                # Detección precisa de take profit
                elif current_price >= (position['take_profit'] * (1 - tolerance)):
                    logger.log(f"🎯 TAKE PROFIT ACTIVADO: Precio ${current_price:.2f} >= TP ${position['take_profit']:.2f}")
                    self.close_position(position, current_price, 'TAKE_PROFIT')

            else:  # SHORT
                # Calcular distancias precisas
                dist_to_sl = (position['stop_loss'] - current_price) / current_price
                dist_to_tp = (current_price - position['take_profit']) / position['take_profit']

                # Alertas de proximidad (más precisas)
                if 0 < dist_to_sl < 0.005:  # Dentro del 0.5%
                    logger.log(f"⚠️ ALERTA CRÍTICA: SHORT muy cerca de SL! Distancia: {dist_to_sl*100:.3f}%")
                elif 0 < dist_to_tp < 0.005:  # Dentro del 0.5%
                    logger.log(f"🎯 ALERTA: SHORT muy cerca de TP! Distancia: {dist_to_tp*100:.3f}%")

                # Detección precisa de stop loss
                if current_price >= (position['stop_loss'] * (1 - tolerance)):
                    logger.log(f"🛑 STOP LOSS ACTIVADO: Precio ${current_price:.2f} >= SL ${position['stop_loss']:.2f}")
                    self.close_position(position, current_price, 'STOP_LOSS')

                # Detección precisa de take profit
                elif current_price <= (position['take_profit'] * (1 + tolerance)):
                    logger.log(f"🎯 TAKE PROFIT ACTIVADO: Precio ${current_price:.2f} <= TP ${position['take_profit']:.2f}")
                    self.close_position(position, current_price, 'TAKE_PROFIT')

    def close_position(self, position, exit_price, reason):
        """Cierra posición con análisis detallado y estadísticas mejoradas"""
        if position['type'] == 'LONG':
            pnl = (exit_price - position['entry_price']) / position['entry_price'] * position['position_size']
            pnl_pct = (exit_price - position['entry_price']) / position['entry_price'] * 100
        else:  # SHORT
            pnl = (position['entry_price'] - exit_price) / position['entry_price'] * position['position_size']
            pnl_pct = (position['entry_price'] - exit_price) / position['entry_price'] * 100

        # Completar información del trade
        position['exit_price'] = exit_price
        position['pnl'] = pnl
        position['pnl_pct'] = pnl_pct
        position['status'] = 'CLOSED'
        position['exit_reason'] = reason
        position['exit_time'] = datetime.datetime.now()
        position['duration'] = (position['exit_time'] - position['entry_time']).seconds // 60

        # Determinar resultado del trade
        trade_result = 'win' if pnl > 0 else 'loss'

        # Actualizar capital y estadísticas
        self.risk_manager.update_capital(pnl, trade_result)

        # Añadir a historial
        self.risk_manager.trade_history.append(position.copy())

        # Log detallado del resultado
        emoji = "💰" if pnl > 0 else "📉"
        result_emoji = "✅ GANANCIA" if pnl > 0 else "❌ PÉRDIDA"

        logger.log(f"\n{'='*70}")
        logger.log(f"{emoji} POSICIÓN CERRADA - {result_emoji}")
        logger.log(f"├─ Tipo: {position['type']}")
        logger.log(f"├─ Entrada: ${position['entry_price']:.2f}")
        logger.log(f"├─ Salida: ${exit_price:.2f}")
        logger.log(f"├─ Razón: {reason}")
        logger.log(f"├─ PnL: ${pnl:.2f} ({pnl_pct:+.2f}%)")
        logger.log(f"├─ Duración: {position['duration']} minutos")
        logger.log(f"├─ Trailing Stop usado: {'Sí' if position.get('trailing_updated', False) else 'No'}")
        logger.log(f"├─ Capital actualizado: ${self.risk_manager.current_capital:.2f}")
        logger.log(f"└─ Drawdown actual: {self.risk_manager.max_drawdown:.2%}")
        logger.log(f"{'='*70}")

        # Estadísticas adicionales
        total_trades = len(self.risk_manager.trade_history)
        if total_trades == 1:
            logger.log(f"\n🎉 ¡PRIMER TRADE COMPLETADO!")
            logger.log(f"💡 Sistema funcionando - Listo para el siguiente trade")
        elif total_trades % 5 == 0:
            # Mostrar estadísticas cada 5 trades
            wins = sum(1 for t in self.risk_manager.trade_history if t['pnl'] > 0)
            win_rate = wins / total_trades * 100
            logger.log(f"\n📊 ESTADÍSTICAS ({total_trades} trades):")
            logger.log(f"├─ Win Rate: {win_rate:.1f}%")
            logger.log(f"├─ Pérdidas consecutivas: {self.risk_manager.consecutive_losses}")
            logger.log(f"└─ ROI Total: {(self.risk_manager.current_capital/self.risk_manager.initial_capital-1)*100:+.2f}%")

        logger.log(f"\n🔄 Sistema listo para el próximo trade único...")

    def generate_report(self):
        """Genera reporte de rendimiento"""
        closed_positions = [p for p in self.active_positions if p['status'] == 'CLOSED']

        # Calcular métricas básicas
        current_capital = self.risk_manager.current_capital
        initial_capital = self.risk_manager.initial_capital
        roi = (current_capital - initial_capital) / initial_capital * 100

        if not closed_positions:
            return {
                'total_trades': 0,
                'win_rate': 0,
                'profit_factor': 0,
                'total_pnl': 0,
                'sharpe_ratio': 0,
                'current_capital': current_capital,
                'roi': roi
            }

        # Calcular métricas
        wins = [p for p in closed_positions if p['pnl'] > 0]
        losses = [p for p in closed_positions if p['pnl'] < 0]

        win_rate = len(wins) / len(closed_positions) * 100

        total_wins = sum(p['pnl'] for p in wins) if wins else 0
        total_losses = abs(sum(p['pnl'] for p in losses)) if losses else 1

        profit_factor = total_wins / total_losses if total_losses > 0 else float('inf')

        # Calcular Sharpe Ratio
        returns = [p['pnl'] / p['position_size'] for p in closed_positions]
        if len(returns) > 1:
            sharpe = np.mean(returns) / (np.std(returns) + 1e-8) * np.sqrt(252)
        else:
            sharpe = 0

        report = {
            'total_trades': len(closed_positions),
            'win_rate': win_rate,
            'profit_factor': profit_factor,
            'total_pnl': sum(p['pnl'] for p in closed_positions),
            'sharpe_ratio': sharpe,
            'current_capital': current_capital,
            'roi': roi
        }

        return report

class LiveTradingBot:
    """Bot principal para trading en vivo (simulado)"""

    def __init__(self):
        self.trader = SimulatedTrader()
        self.is_trained = False
        self.last_signal_time = None
        self.timeframes = ['15m', '1h']
        self.total_trades_executed = 0

    def show_market_summary(self, df):
        """Muestra resumen rápido del mercado"""
        current_price = df['close'].iloc[-1]
        price_change = (current_price - df['close'].iloc[-5]) / df['close'].iloc[-5] * 100
        rsi = talib.RSI(df['close'])[-1]
        volume_avg = df['volume'].rolling(20).mean().iloc[-1]
        volume_ratio = df['volume'].iloc[-1] / volume_avg

        logger.log(f"\n💹 MERCADO: ${current_price:.2f} ({price_change:+.2f}%) | "
                  f"RSI: {rsi:.0f} {'🔴' if rsi > 70 else '🟢' if rsi < 30 else '⚪'} | "
                  f"Vol: {'🔥' if volume_ratio > 1.5 else '📊'} x{volume_ratio:.1f}")

    def initialize(self):
        """Inicializa el bot con datos históricos"""
        logger.log("🚀 Inicializando Trading Bot Profesional v2.0")
        logger.log("📚 Descargando datos históricos...")

        # Obtener datos históricos para entrenamiento
        df = self.trader.data_manager.get_live_data(timeframe='15m', limit=1000)

        if not df.empty:
            logger.log("🤖 Entrenando modelos de machine learning...")
            self.is_trained = self.trader.train_system(df)

            if self.is_trained:
                logger.log("✅ Bot inicializado correctamente")
                logger.log(f"📊 Sistema listo con {len(self.trader.signal_generator.models)} modelos activos")
                logger.log("✨ El sistema está configurado en MODO DEMO para mayor actividad")
                self.show_next_predictions(df)
            else:
                logger.log("❌ Error en inicialización", "ERROR")
        else:
            logger.log("❌ No se pudieron obtener datos", "ERROR")

    def show_next_predictions(self, df):
        """Muestra próximas predicciones con niveles de TP/SL"""
        logger.log("\n" + "="*60)
        logger.log("🔮 PRÓXIMAS PREDICCIONES (MODO SIMULACIÓN)")
        logger.log("="*60)

        # Crear features
        df_features = self.trader.feature_engineer.create_features(df.copy())

        # Generar señales
        signals = self.trader.signal_generator.generate_signals(df_features)

        if not signals.empty:
            # Mostrar últimas 5 señales con TP/SL
            logger.log("\n📊 SEÑALES DE TRADING:")
            for _, signal in signals.tail(5).iterrows():
                direction = "🟢 LONG" if signal['signal'] == 1 else "🔴 SHORT"

                # Calcular TP/SL
                atr = signal['atr'] if signal['atr'] > 0 else signal['price'] * 0.005
                if signal['signal'] == 1:  # LONG
                    stop_loss = signal['price'] - (atr * 1.5)
                    take_profit = signal['price'] + (atr * 2.25)
                else:  # SHORT
                    stop_loss = signal['price'] + (atr * 1.5)
                    take_profit = signal['price'] - (atr * 2.25)

                logger.log(f"\n{direction} @ ${signal['price']:.2f}")
                logger.log(f"├─ Stop Loss: ${stop_loss:.2f} ({abs(stop_loss-signal['price'])/signal['price']*100:.2f}%)")
                logger.log(f"├─ Take Profit: ${take_profit:.2f} ({abs(take_profit-signal['price'])/signal['price']*100:.2f}%)")
                logger.log(f"├─ Confianza: {signal['confidence']:.2%}")
                logger.log(f"└─ Hora: {signal['timestamp']}")
        else:
            logger.log("❌ No hay señales de alta confianza en este momento")

        # Mostrar estado del mercado detallado
        current_price = df['close'].iloc[-1]
        rsi = talib.RSI(df['close'])[-1]
        sma_20 = df['close'].rolling(20).mean().iloc[-1]
        sma_50 = df['close'].rolling(50).mean().iloc[-1]
        volume_avg = df['volume'].rolling(20).mean().iloc[-1]
        volatility = df['close'].pct_change().rolling(20).std().iloc[-1] * 100

        trend = "ALCISTA 📈" if df['close'].iloc[-1] > df['close'].iloc[-20] else "BAJISTA 📉"
        momentum = "FUERTE" if abs(df['close'].iloc[-1] - df['close'].iloc[-5]) / df['close'].iloc[-5] > 0.01 else "DÉBIL"

        logger.log(f"\n📊 ESTADO DEL MERCADO:")
        logger.log(f"├─ Precio actual: ${current_price:.2f}")
        logger.log(f"├─ RSI: {rsi:.2f} {'(Sobrecompra)' if rsi > 70 else '(Sobreventa)' if rsi < 30 else '(Neutral)'}")
        logger.log(f"├─ SMA 20: ${sma_20:.2f} {'↑' if current_price > sma_20 else '↓'}")
        logger.log(f"├─ SMA 50: ${sma_50:.2f} {'↑' if current_price > sma_50 else '↓'}")
        logger.log(f"├─ Tendencia: {trend} ({momentum})")
        logger.log(f"├─ Volatilidad: {volatility:.2f}% {'🔥 ALTA' if volatility > 2 else '✅ NORMAL'}")
        logger.log(f"└─ Volumen: {'📊 Alto' if df['volume'].iloc[-1] > volume_avg * 1.5 else '📉 Normal'}")

    def run_live_simulation(self):
        """Ejecuta bot en modo simulación continua"""
        if not self.is_trained:
            logger.log("❌ Bot no está entrenado", "ERROR")
            logger.log("💡 Por favor, seleccione opción 1 para inicializar el sistema primero")
            return

        logger.log("\n🤖 INICIANDO TRADING EN MODO SIMULACIÓN")
        logger.log(f"💰 Capital inicial: ${self.trader.risk_manager.initial_capital}")
        logger.log("\n📌 El bot ejecutará trades INMEDIATAMENTE cuando detecte señales")
        logger.log("📌 Mostrará predicciones con niveles de TP/SL")
        logger.log("📌 Actualizará posiciones en tiempo real")
        logger.log("📌 Modo DEMO se activará si no hay señales reales")
        logger.log(f"📌 Confianza mínima para trades: {MIN_CONFIDENCE:.0%}\n")

        cycle = 0
        while True:
            try:
                cycle += 1
                logger.log(f"\n{'='*60}")
                logger.log(f"📍 Ciclo #{cycle} - {datetime.datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")

                # Verificar límite diario
                if self.trader.risk_manager.check_daily_loss_limit():
                    logger.log("🛑 Límite diario alcanzado - Trading pausado")
                    time.sleep(3600)
                    continue

                # Obtener datos actuales
                df = self.trader.data_manager.get_live_data(timeframe='15m', limit=200)

                if df.empty:
                    logger.log("⚠️ No hay datos disponibles", "WARNING")
                    time.sleep(60)
                    continue

                # Precio actual
                current_price = df['close'].iloc[-1]

                # Mostrar resumen de mercado
                if cycle <= 10 or cycle % 3 == 0:
                    self.show_market_summary(df)

                # Mostrar información del ciclo
                if cycle == 1:
                    logger.log(f"├─ Sistema: ✅ ACTIVO")
                    logger.log(f"├─ Modelos ML: {len(self.trader.signal_generator.models)} activos")
                    logger.log(f"├─ Confianza mínima: {MIN_CONFIDENCE:.0%}")
                logger.log(f"├─ Precio BTC: ${current_price:.2f}")
                logger.log(f"├─ Posiciones abiertas: {len([p for p in self.trader.active_positions if p['status'] == 'OPEN'])}/3")
                logger.log(f"├─ Trades ejecutados: {self.total_trades_executed}")
                logger.log(f"└─ Capital: ${self.trader.risk_manager.current_capital:.2f}")

                # Actualizar posiciones con precio actual
                self.trader.update_positions(current_price)

                # Generar nuevas señales desde ciclo 2
                if cycle >= 2:
                    logger.log(f"\n🔍 [Ciclo {cycle}] Analizando mercado para oportunidades...")
                    df_features = self.trader.feature_engineer.create_features(df.copy())

                    # Mostrar algunos indicadores clave
                    try:
                        current_rsi = talib.RSI(df['close'])[-1]
                        current_macd = talib.MACD(df['close'])[0][-1]
                        logger.log(f"📊 Indicadores: RSI={current_rsi:.1f}, MACD={current_macd:.1f}")
                    except:
                        pass

                    signals = self.trader.signal_generator.generate_signals(df_features)

                    if not signals.empty:
                        logger.log(f"\n🎯 ¡{len(signals)} SEÑALES DETECTADAS!")

                        # Contar tipos de señales
                        long_signals = len(signals[signals['signal'] == 1])
                        short_signals = len(signals[signals['signal'] == -1])

                        if long_signals > 0:
                            logger.log(f"🟢 LONG: {long_signals} señales")
                        if short_signals > 0:
                            logger.log(f"🔴 SHORT: {short_signals} señales")

                        # Mostrar mejor señal
                        best_signal = signals.loc[signals['confidence'].idxmax()]
                        logger.log(f"🏆 Mejor señal: {'LONG' if best_signal['signal'] == 1 else 'SHORT'} con {best_signal['confidence']:.1%} confianza")

                        # Verificar posiciones abiertas (SOLO UNA PERMITIDA)
                        open_positions = [p for p in self.trader.active_positions if p['status'] == 'OPEN']

                        logger.log(f"📊 Posiciones abiertas: {len(open_positions)}/{MAX_POSITIONS}")

                        if len(open_positions) == 0:
                            # Solo ejecutar si no hay posiciones abiertas
                            logger.log(f"💼 ¡Espacio disponible para UN TRADE!")

                            # Tomar la MEJOR señal por confianza
                            best_signal = signals.loc[signals['confidence'].idxmax()]

                            logger.log(f"\n🔄 Procesando MEJOR señal:")
                            logger.log(f"   Tipo: {'🟢 LONG' if best_signal['signal'] == 1 else '🔴 SHORT'}")
                            logger.log(f"   Precio: ${best_signal['price']:.2f}")
                            logger.log(f"   Confianza: {best_signal['confidence']:.2%}")
                            logger.log(f"   Consenso: {'✅' if best_signal.get('consensus_score', False) else '❌'}")
                            logger.log(f"   Volatilidad OK: {'✅' if best_signal.get('volatility_ok', False) else '❌'}")

                            # Ejecutar el trade único
                            logger.log("   🎯 EJECUTANDO TRADE ÚNICO")
                            try:
                                trade_result = self.trader.execute_trade(best_signal, df)
                                if trade_result:
                                    self.total_trades_executed += 1
                                    logger.log("   ✅ Trade ejecutado exitosamente")
                                    logger.log(f"   📊 Este es el trade #{self.total_trades_executed}")
                                else:
                                    logger.log("   ❌ Trade rechazado por validaciones de riesgo")
                            except Exception as e:
                                logger.log(f"   ❌ Error ejecutando trade: {str(e)}", "ERROR")
                        else:
                            # Ya hay una posición abierta
                            current_position = open_positions[0]
                            entry_time = current_position['entry_time']
                            duration = (datetime.datetime.now() - entry_time).seconds // 60

                            logger.log(f"⏳ TRADE ACTIVO - Esperando cierre:")
                            logger.log(f"   Tipo: {current_position['type']}")
                            logger.log(f"   Entrada: ${current_position['entry_price']:.2f}")
                            logger.log(f"   Duración: {duration} minutos")
                            logger.log(f"   SL: ${current_position['stop_loss']:.2f}")
                            logger.log(f"   TP: ${current_position['take_profit']:.2f}")

                            # Mostrar PnL actual
                            if current_position['type'] == 'LONG':
                                unrealized_pnl = (current_price - current_position['entry_price']) / current_position['entry_price'] * 100
                            else:
                                unrealized_pnl = (current_position['entry_price'] - current_price) / current_position['entry_price'] * 100

                            pnl_emoji = "🟢" if unrealized_pnl > 0 else "🔴" if unrealized_pnl < 0 else "⚪"
                            logger.log(f"   PnL no realizado: {pnl_emoji} {unrealized_pnl:+.2f}%")
                    else:
                        logger.log(f"❌ No hay señales con confianza suficiente (>{MIN_CONFIDENCE:.0%})")
                        # Mostrar info de debug
                        X, _ = self.trader.signal_generator.prepare_data(df_features)
                        if X is not None and len(X) > 0:
                            logger.log(f"🔍 Sistema analizando {len(X)} patrones de mercado...")

                        # MODO DEMO: Generar señal si no hay trades
                        should_generate_demo = (
                            (cycle >= 3 and self.total_trades_executed == 0) or
                            (cycle >= 10 and cycle % 10 == 0 and len(open_positions) == 0)
                        )

                        if should_generate_demo:
                            if self.total_trades_executed == 0:
                                logger.log(f"\n⚡ ACTIVANDO MODO DEMO (Ciclo {cycle}/3 sin trades)")
                            else:
                                logger.log(f"\n⚡ ACTIVANDO MODO DEMO (Sin posiciones abiertas)")
                            logger.log("📌 Generando trade de demostración para mantener actividad...")
                            import random

                            # Obtener RSI
                            try:
                                rsi = talib.RSI(df['close'])[-1]
                            except:
                                rsi = 50

                            # Alternar entre LONG y SHORT
                            if self.total_trades_executed % 2 == 0:
                                signal_direction = 1 if rsi < 50 else -1
                            else:
                                signal_direction = -1 if rsi > 50 else 1

                            demo_signal = pd.Series({
                                'signal': signal_direction,
                                'confidence': 0.55 + random.random() * 0.1,
                                'price': current_price,
                                'timestamp': pd.Timestamp.now(),
                                'atr': df['atr'].iloc[-1] if 'atr' in df.columns and not pd.isna(df['atr'].iloc[-1]) else current_price * 0.005
                            })

                            logger.log(f"🎯 Ejecutando trade DEMO {'LONG' if demo_signal['signal'] == 1 else 'SHORT'}")
                            self.trader.execute_trade(demo_signal, df)
                            self.total_trades_executed += 1

                # Mostrar estado de posiciones abiertas
                open_positions = [p for p in self.trader.active_positions if p['status'] == 'OPEN']
                if open_positions and cycle % 3 == 0:
                    logger.log("\n📋 POSICIONES ABIERTAS:")
                    for i, pos in enumerate(open_positions):
                        pnl_actual = ((current_price - pos['entry_price']) / pos['entry_price'] * 100
                                     if pos['type'] == 'LONG'
                                     else (pos['entry_price'] - current_price) / pos['entry_price'] * 100)

                        # Emoji según PnL
                        pnl_emoji = "🟢" if pnl_actual > 0 else "🔴" if pnl_actual < 0 else "⚪"

                        logger.log(f"{i+1}. {pos['type']} @ ${pos['entry_price']:.2f} | "
                                  f"PnL: {pnl_emoji} {pnl_actual:+.2f}% | "
                                  f"Tiempo: {(datetime.datetime.now() - pos['entry_time']).seconds // 60} min")

                # Mostrar estado cada 5 ciclos
                if cycle % 5 == 0:
                    report = self.trader.generate_report()
                    logger.log(f"\n📊 REPORTE DE RENDIMIENTO:")
                    logger.log(f"├─ Trades totales: {report['total_trades']}")
                    logger.log(f"├─ Win Rate: {report['win_rate']:.1f}%")
                    logger.log(f"├─ Profit Factor: {report['profit_factor']:.2f}")
                    logger.log(f"├─ PnL Total: ${report['total_pnl']:.2f}")
                    logger.log(f"├─ ROI: {report.get('roi', 0):.2f}%")
                    logger.log(f"└─ Capital actual: ${report['current_capital']:.2f}")

                    # Solo mostrar predicciones cada 10 ciclos
                    if cycle % 10 == 0:
                        self.show_next_predictions(df)

                # Esperar antes del próximo ciclo
                time.sleep(30)  # 30 segundos

            except KeyboardInterrupt:
                logger.log("\n🛑 Trading detenido por el usuario")
                break
            except Exception as e:
                logger.log(f"❌ Error en ciclo: {str(e)}", "ERROR")
                time.sleep(60)

        # Reporte final
        logger.log("\n" + "="*60)
        logger.log("📊 REPORTE FINAL")
        logger.log("="*60)

        final_report = self.trader.generate_report()
        for key, value in final_report.items():
            if isinstance(value, float):
                logger.log(f"{key}: {value:.2f}")
            else:
                logger.log(f"{key}: {value}")

def main():
    """Función principal"""
    print("\n" + "="*70)
    print("🚀 BTC TRADING BOT PROFESIONAL v3.0 - MEJORADO")
    print("="*70)
    print("🎯 NUEVAS CARACTERÍSTICAS v3.0:")
    print("  • UN TRADE POR VEZ (máxima precisión)")
    print("  • Detección precisa de TP/SL con tolerancia")
    print("  • Trailing Stop Loss automático")
    print("  • Modelos de ML avanzados (5 algoritmos)")
    print("  • Features técnicos expandidos (100+ indicadores)")
    print("  • Validación cruzada temporal")
    print("  • Filtros de consenso entre modelos")
    print("  • Gestión de riesgo adaptativa")
    print("  • Persistencia de modelos entrenados")
    print("="*70)
    print("⚡ INSTRUCCIONES:")
    print("1. Primero DEBES inicializar el sistema (opción 1)")
    print("2. El bot ejecutará SOLO UN TRADE por vez")
    print("3. Detección precisa de Stop Loss y Take Profit")
    print("4. Trailing Stop se activa automáticamente")
    print("5. Los resultados son 100% simulados - NO es dinero real")
    print("="*70)

    bot = LiveTradingBot()

    # Menú principal
    while True:
        print("\nSeleccione opción:")
        print("1. Inicializar/Reinicializar sistema")
        print("2. Ejecutar simulación en vivo ⭐ (RECOMENDADO)")
        print("3. Ver predicciones actuales")
        print("4. Salir")

        opcion = input("\nOpción (1-4): ")

        if opcion == '1':
            print("\n🔄 Inicializando sistema...")
            print("⏳ Esto puede tomar 30-60 segundos...")
            bot.initialize()
            print("\n✅ Sistema listo para trading!")
            print("💡 Ahora seleccione opción 2 para comenzar la simulación")
        elif opcion == '2':
            if bot.is_trained:
                bot.run_live_simulation()
            else:
                print("❌ Primero debe inicializar el sistema (opción 1)")
                print("💡 El sistema necesita ser entrenado antes de ejecutar la simulación")
        elif opcion == '3':
            if bot.is_trained:
                df = bot.trader.data_manager.get_live_data()
                bot.show_next_predictions(df)
            else:
                print("❌ Primero debe inicializar el sistema (opción 1)")
        elif opcion == '4':
            print("👋 Gracias por usar BTC Trading Bot Pro v2.0")
            break
        else:
            print("❌ Opción inválida")

if __name__ == "__main__":
    main()